package com.iotlaser.spms.common.utils;

import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import org.dromara.common.core.exception.ServiceException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TaxCalculationUtils 单元测试
 * <p>
 * 测试覆盖：
 * - 正常计算场景（基于不同输入参数）
 * - 边界条件测试（null值、零值、负值）
 * - 异常场景测试（参数错误、计算溢出）
 * - 精度验证测试（不同精度要求）
 * - 税率格式转换测试
 * - 计算结果一致性验证
 *
 * <AUTHOR>
 * @date 2025/07/11
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("价税分离计算工具类测试")
class TaxCalculationUtilsTest {

    /**
     * 测试基于不含税单价的计算
     */
    @Test
    @DisplayName("基于不含税单价计算 - 正常场景")
    void testCalculateFromPriceExclusiveTax() {
        // Given
        BigDecimal quantity = new BigDecimal("10");
        BigDecimal taxRate = new BigDecimal("13"); // 13%
        BigDecimal priceExclusiveTax = new BigDecimal("100.00");

        // When
        TaxCalculationUtils.TaxCalculationResult result = TaxCalculationUtils.calculate(
            quantity, taxRate, null, priceExclusiveTax, null, null);

        // Then
        assertNotNull(result);
        assertEquals(quantity, result.getQuantity());
        assertEquals(taxRate, result.getTaxRate());
        assertEquals(priceExclusiveTax, result.getPriceExclusiveTax());
        
        // 验证计算结果
        assertEquals(new BigDecimal("1000.00"), result.getAmountExclusiveTax()); // 100 * 10
        assertEquals(new BigDecimal("130.00"), result.getTaxAmount()); // 1000 * 0.13
        assertEquals(new BigDecimal("113.00"), result.getPrice()); // 100 * 1.13
        assertEquals(new BigDecimal("1130.00"), result.getAmount()); // 1000 + 130
    }

    /**
     * 测试基于含税单价的计算
     */
    @Test
    @DisplayName("基于含税单价计算 - 正常场景")
    void testCalculateFromPrice() {
        // Given
        BigDecimal quantity = new BigDecimal("5");
        BigDecimal taxRate = new BigDecimal("13"); // 13%
        BigDecimal price = new BigDecimal("113.00");

        // When
        TaxCalculationUtils.TaxCalculationResult result = TaxCalculationUtils.calculate(
            quantity, taxRate, price, null, null, null);

        // Then
        assertNotNull(result);
        assertEquals(quantity, result.getQuantity());
        assertEquals(taxRate, result.getTaxRate());
        assertEquals(price, result.getPrice());
        
        // 验证计算结果
        assertEquals(new BigDecimal("565.00"), result.getAmount()); // 113 * 5
        assertEquals(new BigDecimal("100.00"), result.getPriceExclusiveTax()); // 113 / 1.13
        assertEquals(new BigDecimal("500.00"), result.getAmountExclusiveTax()); // 100 * 5
        assertEquals(new BigDecimal("65.00"), result.getTaxAmount()); // 565 - 500
    }

    /**
     * 测试小数税率计算
     */
    @Test
    @DisplayName("小数税率计算 - 正常场景")
    void testCalculateWithDecimalTaxRate() {
        // Given
        BigDecimal quantity = new BigDecimal("2");
        BigDecimal decimalTaxRate = new BigDecimal("0.13"); // 13%
        BigDecimal priceExclusiveTax = new BigDecimal("100.00");

        // When
        TaxCalculationResultBo result = TaxCalculationUtils.calculateWithDecimalTaxRate(
            quantity, decimalTaxRate, null, priceExclusiveTax, null, null);

        // Then
        assertNotNull(result);
        assertEquals(quantity, result.getQuantity());
        assertEquals(new BigDecimal("13"), result.getTaxRate()); // 转换为百分比
        assertEquals(priceExclusiveTax, result.getPriceExclusiveTax());
        
        // 验证计算结果
        assertEquals(new BigDecimal("200.00"), result.getAmountExclusiveTax()); // 100 * 2
        assertEquals(new BigDecimal("26.00"), result.getTaxAmount()); // 200 * 0.13
        assertEquals(new BigDecimal("113.00"), result.getPrice()); // 100 * 1.13
        assertEquals(new BigDecimal("226.00"), result.getAmount()); // 200 + 26
    }

    /**
     * 测试税率格式转换
     */
    @Test
    @DisplayName("税率格式转换测试")
    void testTaxRateConversion() {
        // 小数转百分比
        BigDecimal decimal = new BigDecimal("0.13");
        BigDecimal percentage = TaxCalculationUtils.decimalToPercentage(decimal);
        assertEquals(new BigDecimal("13"), percentage);

        // 百分比转小数
        BigDecimal percentageInput = new BigDecimal("13");
        BigDecimal decimalResult = TaxCalculationUtils.percentageToDecimal(percentageInput);
        assertEquals(0, decimalResult.compareTo(new BigDecimal("0.1300")));
    }

    /**
     * 测试参数验证 - null值
     */
    @Test
    @DisplayName("参数验证 - null值测试")
    void testValidationWithNullValues() {
        // 数量为null
        assertThrows(ServiceException.class, () -> {
            TaxCalculationUtils.calculate(null, new BigDecimal("13"), 
                new BigDecimal("100"), null, null, null);
        });

        // 税率为null
        assertThrows(ServiceException.class, () -> {
            TaxCalculationUtils.calculate(new BigDecimal("1"), null, 
                new BigDecimal("100"), null, null, null);
        });

        // 所有价格金额参数都为null
        assertThrows(ServiceException.class, () -> {
            TaxCalculationUtils.calculate(new BigDecimal("1"), new BigDecimal("13"), 
                null, null, null, null);
        });
    }

    /**
     * 测试参数验证 - 边界值
     */
    @Test
    @DisplayName("参数验证 - 边界值测试")
    void testValidationWithBoundaryValues() {
        // 数量为0
        assertThrows(ServiceException.class, () -> {
            TaxCalculationUtils.calculate(BigDecimal.ZERO, new BigDecimal("13"), 
                new BigDecimal("100"), null, null, null);
        });

        // 数量为负数
        assertThrows(ServiceException.class, () -> {
            TaxCalculationUtils.calculate(new BigDecimal("-1"), new BigDecimal("13"), 
                new BigDecimal("100"), null, null, null);
        });

        // 税率为负数
        assertThrows(ServiceException.class, () -> {
            TaxCalculationUtils.calculate(new BigDecimal("1"), new BigDecimal("-1"), 
                new BigDecimal("100"), null, null, null);
        });

        // 税率超过100%
        assertThrows(ServiceException.class, () -> {
            TaxCalculationUtils.calculate(new BigDecimal("1"), new BigDecimal("101"), 
                new BigDecimal("100"), null, null, null);
        });
    }

    /**
     * 测试单独计算方法
     */
    @Test
    @DisplayName("单独计算方法测试")
    void testIndividualCalculationMethods() {
        // 测试计算税额
        BigDecimal taxAmount = TaxCalculationUtils.calculateTaxAmount(
            new BigDecimal("1000"), new BigDecimal("13"));
        assertEquals(new BigDecimal("130.00"), taxAmount);

        // 测试计算不含税金额
        BigDecimal amountExclusiveTax = TaxCalculationUtils.calculateAmountExclusiveTax(
            new BigDecimal("1130"), new BigDecimal("13"));
        assertEquals(new BigDecimal("1000.00"), amountExclusiveTax);

        // 测试计算含税单价
        BigDecimal inclusivePrice = TaxCalculationUtils.calculateInclusivePrice(
            new BigDecimal("100"), new BigDecimal("13"));
        assertEquals(new BigDecimal("113.00"), inclusivePrice);

        // 测试计算不含税单价
        BigDecimal exclusivePrice = TaxCalculationUtils.calculateExclusivePrice(
            new BigDecimal("113"), new BigDecimal("13"));
        assertEquals(new BigDecimal("100.00"), exclusivePrice);
    }

    /**
     * 测试计算结果一致性验证
     */
    @Test
    @DisplayName("计算结果一致性验证测试")
    void testCalculationConsistency() {
        // 创建一个正确的计算结果
        TaxCalculationUtils.TaxCalculationResult result = TaxCalculationUtils.calculate(
            new BigDecimal("10"), new BigDecimal("13"), 
            null, new BigDecimal("100"), null, null);

        // 验证一致性
        assertTrue(TaxCalculationUtils.validateCalculationConsistency(result));

        // 创建一个不一致的结果
        result.setTaxAmount(new BigDecimal("999.99")); // 错误的税额
        assertFalse(TaxCalculationUtils.validateCalculationConsistency(result));
    }

    /**
     * 测试精度处理
     */
    @Test
    @DisplayName("精度处理测试")
    void testPrecisionHandling() {
        // 使用会产生无限小数的除法
        BigDecimal quantity = new BigDecimal("3");
        BigDecimal taxRate = new BigDecimal("13");
        BigDecimal amount = new BigDecimal("100");

        TaxCalculationUtils.TaxCalculationResult result = TaxCalculationUtils.calculate(
            quantity, taxRate, null, null, amount, null);

        // 验证所有金额字段都有正确的精度（2位小数）
        assertEquals(2, result.getAmount().scale());
        assertEquals(2, result.getAmountExclusiveTax().scale());
        assertEquals(2, result.getTaxAmount().scale());
        assertEquals(2, result.getPrice().scale());
        assertEquals(2, result.getPriceExclusiveTax().scale());
    }

    /**
     * 测试BO对象转换
     */
    @Test
    @DisplayName("BO对象转换测试")
    void testBoConversion() {
        // 创建计算结果
        TaxCalculationUtils.TaxCalculationResult internalResult = TaxCalculationUtils.calculate(
            new BigDecimal("5"), new BigDecimal("13"), 
            null, new BigDecimal("100"), null, null);

        // 转换为BO对象
        TaxCalculationResultBo bo = TaxCalculationResultBo.fromInternalResult(internalResult);
        
        // 验证转换结果
        assertNotNull(bo);
        assertEquals(internalResult.getQuantity(), bo.getQuantity());
        assertEquals(internalResult.getTaxRate(), bo.getTaxRate());
        assertEquals(internalResult.getAmount(), bo.getAmount());
        assertEquals(internalResult.getAmountExclusiveTax(), bo.getAmountExclusiveTax());
        assertEquals(internalResult.getTaxAmount(), bo.getTaxAmount());

        // 测试反向转换
        TaxCalculationUtils.TaxCalculationResult converted = bo.toInternalResult();
        assertEquals(internalResult.getQuantity(), converted.getQuantity());
        assertEquals(internalResult.getTaxRate(), converted.getTaxRate());
        assertEquals(internalResult.getAmount(), converted.getAmount());
    }
}
