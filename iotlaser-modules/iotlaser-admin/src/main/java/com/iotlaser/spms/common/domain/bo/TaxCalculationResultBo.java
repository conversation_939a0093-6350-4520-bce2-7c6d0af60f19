package com.iotlaser.spms.common.domain.bo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 价税分离计算结果业务对象
 * <p>
 * 统一的价税分离计算结果封装，确保跨模块数据传输的一致性
 *
 * <AUTHOR> <PERSON>
 * @date 2025/07/11
 */
@Data
@Builder
public class TaxCalculationResultBo {

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 单价(含税)
     */
    private BigDecimal price;

    /**
     * 单价(不含税)
     */
    private BigDecimal priceExclusiveTax;

    /**
     * 金额(含税)
     */
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    private BigDecimal amountExclusiveTax;

    /**
     * 税率(%)
     * 注意：此处使用百分比表示，如13表示13%
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 转换为内部结果对象
     * 
     * @return TaxCalculationUtils.TaxCalculationResult
     */
    public com.iotlaser.spms.common.utils.TaxCalculationUtils.TaxCalculationResult toInternalResult() {
        com.iotlaser.spms.common.utils.TaxCalculationUtils.TaxCalculationResult result = 
            new com.iotlaser.spms.common.utils.TaxCalculationUtils.TaxCalculationResult();
        
        result.setQuantity(this.quantity);
        result.setPrice(this.price);
        result.setPriceExclusiveTax(this.priceExclusiveTax);
        result.setAmount(this.amount);
        result.setAmountExclusiveTax(this.amountExclusiveTax);
        result.setTaxRate(this.taxRate);
        result.setTaxAmount(this.taxAmount);
        
        return result;
    }

    /**
     * 从内部结果对象创建
     * 
     * @param internalResult 内部结果对象
     * @return TaxCalculationResultBo
     */
    public static TaxCalculationResultBo fromInternalResult(
            com.iotlaser.spms.common.utils.TaxCalculationUtils.TaxCalculationResult internalResult) {
        
        return TaxCalculationResultBo.builder()
            .quantity(internalResult.getQuantity())
            .price(internalResult.getPrice())
            .priceExclusiveTax(internalResult.getPriceExclusiveTax())
            .amount(internalResult.getAmount())
            .amountExclusiveTax(internalResult.getAmountExclusiveTax())
            .taxRate(internalResult.getTaxRate())
            .taxAmount(internalResult.getTaxAmount())
            .build();
    }

    /**
     * 转换为 PriceCalculationResult（兼容现有业务）
     * 
     * @return PriceCalculationResult
     */
    public PriceCalculationResult toPriceCalculationResult() {
        // 将百分比税率转换为小数税率
        BigDecimal decimalTaxRate = this.taxRate != null ? 
            this.taxRate.divide(new BigDecimal("100"), 4, java.math.RoundingMode.HALF_UP) : null;
        
        return PriceCalculationResult.builder()
            .quantity(this.quantity)
            .price(this.price)
            .priceExclusiveTax(this.priceExclusiveTax)
            .amount(this.amount)
            .amountExclusiveTax(this.amountExclusiveTax)
            .taxRate(decimalTaxRate) // 注意：这里转换为小数格式
            .taxAmount(this.taxAmount)
            .build();
    }

    /**
     * 从 PriceCalculationResult 创建（兼容现有业务）
     * 
     * @param priceResult PriceCalculationResult
     * @return TaxCalculationResultBo
     */
    public static TaxCalculationResultBo fromPriceCalculationResult(PriceCalculationResult priceResult) {
        // 将小数税率转换为百分比税率
        BigDecimal percentageTaxRate = priceResult.getTaxRate() != null ? 
            priceResult.getTaxRate().multiply(new BigDecimal("100")) : null;
        
        return TaxCalculationResultBo.builder()
            .quantity(priceResult.getQuantity())
            .price(priceResult.getPrice())
            .priceExclusiveTax(priceResult.getPriceExclusiveTax())
            .amount(priceResult.getAmount())
            .amountExclusiveTax(priceResult.getAmountExclusiveTax())
            .taxRate(percentageTaxRate) // 注意：这里转换为百分比格式
            .taxAmount(priceResult.getTaxAmount())
            .build();
    }
}
