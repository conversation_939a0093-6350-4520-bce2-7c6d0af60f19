package com.iotlaser.spms.common.service.impl;

import com.iotlaser.spms.common.domain.bo.PriceCalculationResult;
import com.iotlaser.spms.common.domain.bo.TaxCalculationResultBo;
import com.iotlaser.spms.common.service.IPriceCalculationService;
import com.iotlaser.spms.common.utils.TaxCalculationUtils;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 价格计算服务实现类
 * <p>
 * 重构后的实现，内部调用 TaxCalculationUtils 进行统一的价税分离计算
 * 专注于业务流程编排和数据格式转换，确保与现有业务的兼容性
 *
 * <AUTHOR> Kai
 * @date 2025/06/24
 * @version 2.0 - 2025/07/11 重构为调用统一工具类
 */
@Slf4j
@Service
public class PriceCalculationServiceImpl implements IPriceCalculationService {

    /**
     * 默认精度：4位小数（保持与原有业务的兼容性）
     */
    private static final int DEFAULT_SCALE = 4;

    /**
     * 默认舍入模式：四舍五入
     */
    private static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 基于含税单价计算价税分离结果
     * <p>
     * 重构后的实现，内部调用 TaxCalculationUtils 进行统一计算
     *
     * @param price    单价(含税)
     * @param taxRate  税率（小数格式，如 0.13 表示 13%）
     * @param quantity 数量
     * @return 计算结果
     * @throws ServiceException 当计算失败时
     */
    @Override
    public PriceCalculationResult calculateFromInclusivePrice(BigDecimal price, BigDecimal taxRate, BigDecimal quantity) {
        log.debug("开始价格计算(含税单价) - 含税单价: {}, 税率: {}, 数量: {}", price, taxRate, quantity);

        try {
            // 调用统一工具类进行计算（注意税率格式转换）
            TaxCalculationResultBo result = TaxCalculationUtils.calculateWithDecimalTaxRate(
                quantity, taxRate, price, null, null, null);

            // 转换为业务层结果对象（保持原有精度设置）
            PriceCalculationResult priceResult = result.toPriceCalculationResult();

            // 调整精度以保持与原有业务的兼容性
            priceResult = adjustPrecisionForCompatibility(priceResult);

            log.info("价格计算完成(含税单价) - 不含税金额: {}, 税额: {}, 含税金额: {}",
                priceResult.getAmountExclusiveTax(), priceResult.getTaxAmount(), priceResult.getAmount());

            return priceResult;

        } catch (Exception e) {
            log.error("价格计算失败(含税单价): {}", e.getMessage(), e);
            throw new ServiceException("价格计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 计算含税金额
     * <p>
     * 重构后的实现，使用统一的计算逻辑和异常处理
     *
     * @param quantity 数量
     * @param price    单价(含税)
     * @return 含税金额
     * @throws ServiceException 当计算失败时
     */
    @Override
    public BigDecimal calculateAmount(BigDecimal quantity, BigDecimal price) {
        log.debug("计算含税金额 - 数量: {}, 含税单价: {}", quantity, price);

        if (quantity == null || price == null) {
            log.warn("计算含税金额时参数为空，返回0 - 数量: {}, 含税单价: {}", quantity, price);
            return BigDecimal.ZERO;
        }

        try {
            BigDecimal result = quantity.multiply(price).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
            log.debug("含税金额计算完成 - 结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("含税金额计算失败 - 数量: {}, 含税单价: {}, 错误: {}", quantity, price, e.getMessage());
            throw new ServiceException("含税金额计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 计算不含税金额
     * <p>
     * 重构后的实现，使用统一的计算逻辑和异常处理
     *
     * @param quantity          数量
     * @param priceExclusiveTax 单价(不含税)
     * @return 不含税金额
     * @throws ServiceException 当计算失败时
     */
    @Override
    public BigDecimal calculateAmountExclusiveTax(BigDecimal quantity, BigDecimal priceExclusiveTax) {
        log.debug("计算不含税金额 - 数量: {}, 不含税单价: {}", quantity, priceExclusiveTax);

        if (quantity == null || priceExclusiveTax == null) {
            log.warn("计算不含税金额时参数为空，返回0 - 数量: {}, 不含税单价: {}", quantity, priceExclusiveTax);
            return BigDecimal.ZERO;
        }

        try {
            BigDecimal result = quantity.multiply(priceExclusiveTax).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
            log.debug("不含税金额计算完成 - 结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("不含税金额计算失败 - 数量: {}, 不含税单价: {}, 错误: {}", quantity, priceExclusiveTax, e.getMessage());
            throw new ServiceException("不含税金额计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 计算税额
     * <p>
     * 重构后的实现，内部调用 TaxCalculationUtils 进行统一计算
     *
     * @param amountExclusiveTax 不含税金额
     * @param taxRate            税率（小数格式，如 0.13 表示 13%）
     * @return 税额
     * @throws ServiceException 当计算失败时
     */
    @Override
    public BigDecimal calculateTaxAmount(BigDecimal amountExclusiveTax, BigDecimal taxRate) {
        log.debug("计算税额 - 不含税金额: {}, 税率: {}", amountExclusiveTax, taxRate);

        if (amountExclusiveTax == null || taxRate == null) {
            log.warn("计算税额时参数为空，返回0 - 不含税金额: {}, 税率: {}", amountExclusiveTax, taxRate);
            return BigDecimal.ZERO;
        }

        try {
            // 转换为百分比税率并调用统一工具类
            BigDecimal percentageTaxRate = TaxCalculationUtils.decimalToPercentage(taxRate);
            BigDecimal result = TaxCalculationUtils.calculateTaxAmount(amountExclusiveTax, percentageTaxRate);

            // 调整精度以保持与原有业务的兼容性
            result = result.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);

            log.debug("税额计算完成 - 结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("税额计算失败 - 不含税金额: {}, 税率: {}, 错误: {}", amountExclusiveTax, taxRate, e.getMessage());
            throw new ServiceException("税额计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据含税单价和税率计算不含税单价
     * <p>
     * 重构后的实现，内部调用 TaxCalculationUtils 进行统一计算
     *
     * @param price   含税单价
     * @param taxRate 税率（小数格式，如 0.13 表示 13%）
     * @return 不含税单价
     * @throws ServiceException 当计算失败时
     */
    @Override
    public BigDecimal calculatePriceExclusiveTax(BigDecimal price, BigDecimal taxRate) {
        log.debug("计算不含税单价 - 含税单价: {}, 税率: {}", price, taxRate);

        if (price == null || taxRate == null) {
            log.warn("计算不含税单价时参数为空，返回0 - 含税单价: {}, 税率: {}", price, taxRate);
            return BigDecimal.ZERO;
        }

        try {
            // 转换为百分比税率并调用统一工具类
            BigDecimal percentageTaxRate = TaxCalculationUtils.decimalToPercentage(taxRate);
            BigDecimal result = TaxCalculationUtils.calculateExclusivePrice(price, percentageTaxRate);

            // 调整精度以保持与原有业务的兼容性
            result = result.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);

            log.debug("不含税单价计算完成 - 结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("不含税单价计算失败 - 含税单价: {}, 税率: {}, 错误: {}", price, taxRate, e.getMessage());
            throw new ServiceException("不含税单价计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据不含税单价和税率计算含税单价
     * <p>
     * 重构后的实现，内部调用 TaxCalculationUtils 进行统一计算
     *
     * @param priceExclusiveTax 不含税单价
     * @param taxRate           税率（小数格式，如 0.13 表示 13%）
     * @return 含税单价
     * @throws ServiceException 当计算失败时
     */
    @Override
    public BigDecimal calculatePrice(BigDecimal priceExclusiveTax, BigDecimal taxRate) {
        log.debug("计算含税单价 - 不含税单价: {}, 税率: {}", priceExclusiveTax, taxRate);

        if (priceExclusiveTax == null || taxRate == null) {
            log.warn("计算含税单价时参数为空，返回0 - 不含税单价: {}, 税率: {}", priceExclusiveTax, taxRate);
            return BigDecimal.ZERO;
        }

        try {
            // 转换为百分比税率并调用统一工具类
            BigDecimal percentageTaxRate = TaxCalculationUtils.decimalToPercentage(taxRate);
            BigDecimal result = TaxCalculationUtils.calculateInclusivePrice(priceExclusiveTax, percentageTaxRate);

            // 调整精度以保持与原有业务的兼容性
            result = result.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);

            log.debug("含税单价计算完成 - 结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("含税单价计算失败 - 不含税单价: {}, 税率: {}, 错误: {}", priceExclusiveTax, taxRate, e.getMessage());
            throw new ServiceException("含税单价计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调整精度以保持与原有业务的兼容性
     * <p>
     * 将 TaxCalculationUtils 的2位小数精度调整为4位小数精度
     *
     * @param result 计算结果
     * @return 调整精度后的结果
     */
    private PriceCalculationResult adjustPrecisionForCompatibility(PriceCalculationResult result) {
        if (result == null) {
            return null;
        }

        return PriceCalculationResult.builder()
            .quantity(result.getQuantity())
            .price(adjustScale(result.getPrice()))
            .priceExclusiveTax(adjustScale(result.getPriceExclusiveTax()))
            .amount(adjustScale(result.getAmount()))
            .amountExclusiveTax(adjustScale(result.getAmountExclusiveTax()))
            .taxRate(result.getTaxRate()) // 税率保持原有精度
            .taxAmount(adjustScale(result.getTaxAmount()))
            .build();
    }

    /**
     * 调整单个数值的精度
     */
    private BigDecimal adjustScale(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 校验价格计算的一致性
     * <p>
     * 重构后的实现，增强了错误日志和异常处理
     *
     * @param quantity           数量
     * @param price              含税单价
     * @param priceExclusiveTax  不含税单价
     * @param amount             含税金额
     * @param amountExclusiveTax 不含税金额
     * @param taxRate            税率
     * @param taxAmount          税额
     * @return 是否一致
     */
    @Override
    public boolean validatePriceConsistency(BigDecimal quantity, BigDecimal price, BigDecimal priceExclusiveTax,
                                            BigDecimal amount, BigDecimal amountExclusiveTax,
                                            BigDecimal taxRate, BigDecimal taxAmount) {
        log.debug("开始价格一致性校验 - 数量: {}, 含税单价: {}, 不含税单价: {}, 含税金额: {}, 不含税金额: {}, 税率: {}, 税额: {}",
            quantity, price, priceExclusiveTax, amount, amountExclusiveTax, taxRate, taxAmount);

        try {
            // 使用统一工具类进行一致性验证
            // 首先构建内部结果对象
            TaxCalculationUtils.TaxCalculationResult internalResult = new TaxCalculationUtils.TaxCalculationResult();
            internalResult.setQuantity(quantity);
            internalResult.setPrice(price);
            internalResult.setPriceExclusiveTax(priceExclusiveTax);
            internalResult.setAmount(amount);
            internalResult.setAmountExclusiveTax(amountExclusiveTax);
            internalResult.setTaxRate(TaxCalculationUtils.decimalToPercentage(taxRate)); // 转换税率格式
            internalResult.setTaxAmount(taxAmount);

            // 调用统一工具类的验证方法
            boolean isConsistent = TaxCalculationUtils.validateCalculationConsistency(internalResult);

            if (!isConsistent) {
                log.warn("价格一致性校验失败 - 详细信息已在工具类中记录");
            } else {
                log.debug("价格一致性校验通过");
            }

            return isConsistent;

        } catch (Exception e) {
            log.error("价格一致性校验异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查两个数值是否在容差范围内
     */
    private boolean isWithinTolerance(BigDecimal actual, BigDecimal expected, BigDecimal tolerance) {
        if (actual == null && expected == null) {
            return true;
        }
        if (actual == null || expected == null) {
            return false;
        }
        BigDecimal difference = actual.subtract(expected).abs();
        return difference.compareTo(tolerance) <= 0;
    }
}
