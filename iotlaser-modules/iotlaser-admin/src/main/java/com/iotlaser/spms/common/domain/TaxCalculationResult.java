package com.iotlaser.spms.common.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 价税分离计算结果（内部类，保持向后兼容）
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaxCalculationResult {
    private BigDecimal quantity;
    private BigDecimal price;                // 单价(含税)
    private BigDecimal priceExclusiveTax;    // 单价(不含税)
    private BigDecimal amount;          // 金额(含税)
    private BigDecimal amountExclusiveTax;   // 金额(不含税)
    private BigDecimal taxRate;              // 税率
    private BigDecimal taxAmount;            // 税额
}
